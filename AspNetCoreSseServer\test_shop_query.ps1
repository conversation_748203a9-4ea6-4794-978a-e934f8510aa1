#!/usr/bin/env pwsh

# Test script to verify ShopList queries work without SqlNullValueException

Write-Host "Testing ShopList queries..." -ForegroundColor Green

# Test 1: Search all shops
Write-Host "`nTest 1: Searching all shops..." -ForegroundColor Yellow
$body1 = @{
    method = "tools/call"
    params = @{
        name = "SearchShopsAsync"
        arguments = @{
            keyword = ""
            page = 1
            pageSize = 5
        }
    }
} | ConvertTo-Json -Depth 10

try {
    $response1 = Invoke-RestMethod -Uri "http://localhost:3001/mcp" -Method Post -Body $body1 -ContentType "application/json"
    Write-Host "✅ Test 1 passed: " -ForegroundColor Green -NoNewline
    Write-Host $response1.result.content[0].text
} catch {
    Write-Host "❌ Test 1 failed: $($_.Exception.Message)" -ForegroundColor Red
}

# Test 2: Search shops with keyword
Write-Host "`nTest 2: Searching shops with keyword..." -ForegroundColor Yellow
$body2 = @{
    method = "tools/call"
    params = @{
        name = "SearchShopsAsync"
        arguments = @{
            keyword = "店"
            page = 1
            pageSize = 3
        }
    }
} | ConvertTo-Json -Depth 10

try {
    $response2 = Invoke-RestMethod -Uri "http://localhost:3001/mcp" -Method Post -Body $body2 -ContentType "application/json"
    Write-Host "✅ Test 2 passed: " -ForegroundColor Green -NoNewline
    Write-Host $response2.result.content[0].text
} catch {
    Write-Host "❌ Test 2 failed: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`n🎉 Shop query tests completed!" -ForegroundColor Green
