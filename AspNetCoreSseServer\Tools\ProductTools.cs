using System.ComponentModel;
using System.Text;
using Microsoft.EntityFrameworkCore;
using ModelContextProtocol.Server;
using TestServerWithHosting.DataAccess;
using TestServerWithHosting.DataAccess.Entities;
using HeartFlower.Mall.Shop;
using HeartFlower.Mall.Shop.Dto;
using Abp.Application.Services.Dto;

namespace TestServerWithHosting.Tools;

[McpServerToolType]
public class ProductTools
{
    private readonly IProductAppService _productAppService;
    private readonly IShopListAppService _shopListAppService;
    private readonly IRepository<Product, Guid> _productRepository;
    private readonly IRepository<ProductStock, Guid> _productStockRepository;
    private readonly IRepository<ShopList, Guid> _shopListRepository;

    public ProductTools(
        IProductAppService productAppService,
        IShopListAppService shopListAppService,
        IRepository<Product, Guid> productRepository,
        IRepository<ProductStock, Guid> productStockRepository,
        IRepository<ShopList, Guid> shopListRepository)
    {
        _productAppService = productAppService;
        _shopListAppService = shopListAppService;
        _productRepository = productRepository;
        _productStockRepository = productStockRepository;
        _shopListRepository = shopListRepository;
    }

    [McpServerTool, Description("根据商品ID获取商品详细信息")]
    public async Task<string> GetProductByIdAsync(
        [Description("商品ID")] string productId)
    {
        try
        {
            if (!Guid.TryParse(productId, out var id))
            {
                return "错误：无效的商品ID格式";
            }

            // 使用 Application 层的接口获取商品详情
            var productDetail = await _productAppService.GetProductDetail(id);
            
            if (productDetail == null)
            {
                return "未找到指定的商品";
            }

            var result = new StringBuilder();
            result.AppendLine("商品详细信息：");
            result.AppendLine($"ID: {productDetail.Id}");
            result.AppendLine($"商品名称: {productDetail.ProductName ?? "未设置"}");
            result.AppendLine($"商品描述: {productDetail.ProductDescribe ?? "无描述"}");
            result.AppendLine($"所属店铺: {productDetail.ShopName ?? "未知店铺"}");
            result.AppendLine($"创建时间: {productDetail.CreationTime:yyyy-MM-dd HH:mm:ss}");
            
            if (productDetail.Price.HasValue)
            {
                result.AppendLine($"价格: ¥{productDetail.Price.Value}");
            }

            // 显示库存规格信息
            if (productDetail.StockList != null && productDetail.StockList.Any())
            {
                result.AppendLine("库存规格:");
                foreach (var stock in productDetail.StockList)
                {
                    result.AppendLine($"  规格: {stock.StockName ?? "默认规格"}");
                    result.AppendLine($"  价格: ¥{stock.Price}");
                    if (stock.StockNumber.HasValue)
                    {
                        result.AppendLine($"  库存数量: {stock.StockNumber.Value}");
                    }
                    result.AppendLine();
                }
            }
            else
            {
                result.AppendLine("库存规格: 暂无库存信息");
            }

            return result.ToString();
        }
        catch (Exception ex)
        {
            return $"获取商品信息时发生错误: {ex.Message}";
        }
    }

    [McpServerTool, Description("搜索商品信息，支持按关键词搜索")]
    public async Task<string> SearchProductsAsync(
        [Description("搜索关键词，为空则返回所有商品")] string keyword = "",
        [Description("页码，从1开始")] int page = 1,
        [Description("每页数量")] int pageSize = 10)
    {
        try
        {
            // 使用 Application 层的接口进行商品搜索
            var input = new MallProductListPageRequestDto
            {
                ProductName = keyword,
                MaxResultCount = pageSize,
                SkipCount = (page - 1) * pageSize
            };

            var result = await _productAppService.GetMallList(input);
            
            if (result.Items == null || !result.Items.Any())
            {
                return $"未找到符合条件的商品。关键词: {keyword}";
            }

            var output = new StringBuilder();
            output.AppendLine($"搜索结果 (第{page}页, 共{result.TotalCount}条记录):");
            output.AppendLine($"关键词: {keyword}");
            output.AppendLine();

            foreach (var product in result.Items)
            {
                output.AppendLine($"ID: {product.Id}, 名称: {product.ProductName ?? "未设置"}, 店铺: {product.ShopName ?? "未知店铺"}, 创建时间: {product.CreationTime:yyyy-MM-dd}");
                if (!string.IsNullOrEmpty(product.ProductDescribe))
                {
                    output.AppendLine($"  描述: {product.ProductDescribe}");
                }
                if (product.Price.HasValue)
                {
                    output.AppendLine($"  价格: ¥{product.Price.Value}");
                }
                output.AppendLine();
            }

            return output.ToString();
        }
        catch (Exception ex)
        {
            return $"搜索商品时发生错误: {ex.Message}";
        }
    }

    [McpServerTool, Description("获取商品库存信息列表")]
    public async Task<string> GetLowStockProductsAsync(
        [Description("库存阈值（当前不可用，仅用于兼容性）")] int threshold = 10,
        [Description("页码，从1开始")] int page = 1,
        [Description("每页数量")] int pageSize = 10)
    {
        try
        {
            var query = from stock in _productStockRepository.GetAll()
                       join product in _productRepository.GetAll() on stock.ProductId equals product.Id
                       where !stock.IsDeleted && !product.IsDeleted
                       select new { Product = product, Stock = stock };

            var totalCount = await query.CountAsync();
            
            // 按商品创建时间排序
            var items = await query
                .OrderBy(x => x.Product.CreationTime)
                .Skip((page - 1) * pageSize)
                .Take(pageSize)
                .ToListAsync();

            if (!items.Any())
            {
                return $"没有找到商品库存信息";
            }

            var result = new StringBuilder();
            result.AppendLine($"商品库存信息 (第{page}页, 共{totalCount}条记录, 注意: 数据库未包含库存数量字段):");
            result.AppendLine();

            foreach (var item in items)
            {
                var shop = await _shopListRepository.FirstOrDefaultAsync(s => s.Id == item.Product.ShopId);
                var shopName = shop?.ShopName ?? "未知店铺";
                
                result.AppendLine($"商品: {item.Product.ProductName ?? "未设置"}");
                result.AppendLine($"规格: {item.Stock.StockName ?? "默认规格"}");
                result.AppendLine($"库存数量: 数据库字段不可用");
                result.AppendLine($"价格: ¥{item.Stock.Price}");
                result.AppendLine($"店铺: {shopName}");
                result.AppendLine("---");
            }

            return result.ToString();
        }
        catch (Exception ex)
        {
            return $"获取商品库存信息时发生错误: {ex.Message}";
        }
    }
}