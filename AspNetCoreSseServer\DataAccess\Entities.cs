using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace TestServerWithHosting.DataAccess.Entities;

[Table("AbpUsers")]
public class User
{
    [Key]
    public long Id { get; set; }
    public string? UserName { get; set; }
    public string? Name { get; set; }
    public string? EmailAddress { get; set; }
    public string? PhoneNumber { get; set; }
    public bool IsActive { get; set; }
    public bool IsEmailConfirmed { get; set; }
    public bool IsPhoneNumberConfirmed { get; set; }
    public DateTime CreationTime { get; set; }
    public bool IsDeleted { get; set; }
}

[Table("Product")]
public class Product
{
    [Key]
    public Guid Id { get; set; }
    public string? ProductName { get; set; }
    public string? ProductDescribe { get; set; }
    public Guid ShopId { get; set; }
    public DateTime CreationTime { get; set; }
    public bool IsDeleted { get; set; }
}

[Table("ProductStock")]
public class ProductStock
{
    [Key]
    public Guid Id { get; set; }
    public Guid ProductId { get; set; }
    public string? StockName { get; set; }
    public decimal Price { get; set; }
    public int StockNumber { get; set; }
    public bool IsDeleted { get; set; }
}

[Table("Order")]  
public class Order
{
    [Key]
    public Guid Id { get; set; }
    public string? OrderNumber { get; set; }
    public int Userid { get; set; }
    public Guid ShopId { get; set; }
    public decimal Total { get; set; }
    public decimal RealityPay { get; set; }
    public decimal ShipPrice { get; set; }
    public int OrderStatus { get; set; }
    public DateTime CreationTime { get; set; }
    public DateTime? LastModificationTime { get; set; }
    public bool IsDeleted { get; set; }
}

[Table("OrderDetail")]
public class OrderDetail
{
    [Key]
    public Guid Id { get; set; }
    public Guid OrderId { get; set; }
    public Guid ProductId { get; set; }
    public string? ProductName { get; set; }
    public string? StockName { get; set; }
    public int Amount { get; set; }
    public decimal Price { get; set; }
    public bool IsDeleted { get; set; }
}

[Table("ShopList")]
public class ShopList
{
    [Key]
    public Guid Id { get; set; }
    public string? ShopName { get; set; }
    public string? Linkman { get; set; }
    public string? PhoneNumber { get; set; }
    public string? Remarks { get; set; }
    public DateTime CreationTime { get; set; }
    public bool IsDeleted { get; set; }
}

[Table("ShopCart")]
public class ShopCart
{
    [Key]
    public Guid Id { get; set; }
    public int Userid { get; set; }
    public Guid ProductId { get; set; }
    public Guid StockId { get; set; }
    public int Amount { get; set; }
    public DateTime CreationTime { get; set; }
    public bool IsDeleted { get; set; }
}