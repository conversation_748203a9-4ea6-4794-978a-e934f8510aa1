# 商品工具测试脚本
Write-Host "测试商品工具功能..." -ForegroundColor Green

$serverUrl = "http://localhost:5000"

# 测试商品搜索
Write-Host ""
Write-Host "测试商品搜索..." -ForegroundColor Yellow

$searchRequest = @{
    method = "tools/call"
    params = @{
        name = "SearchProductsAsync"
        arguments = @{
            keyword = ""
            page = 1
            pageSize = 5
        }
    }
} | ConvertTo-Json -Depth 3

try {
    $response = Invoke-RestMethod -Uri "$serverUrl/sse" -Method Post -Body $searchRequest -ContentType "application/json"
    Write-Host "商品搜索结果:" -ForegroundColor Green
    Write-Host $response -ForegroundColor White
} catch {
    Write-Host "商品搜索失败: $($_.Exception.Message)" -ForegroundColor Red
}

# 测试低库存商品查询
Write-Host ""
Write-Host "测试低库存商品查询..." -ForegroundColor Yellow

$lowStockRequest = @{
    method = "tools/call"
    params = @{
        name = "GetLowStockProductsAsync"
        arguments = @{
            threshold = 50
            page = 1
            pageSize = 5
        }
    }
} | ConvertTo-Json -Depth 3

try {
    $response = Invoke-RestMethod -Uri "$serverUrl/sse" -Method Post -Body $lowStockRequest -ContentType "application/json"
    Write-Host "低库存商品结果:" -ForegroundColor Green
    Write-Host $response -ForegroundColor White
} catch {
    Write-Host "低库存商品查询失败: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""
Write-Host "测试完成!" -ForegroundColor Green