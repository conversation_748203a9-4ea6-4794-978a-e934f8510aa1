# 测试 Application 层集成
Write-Host "测试 Application 层集成..." -ForegroundColor Green

# 首先停止现有服务器
Write-Host "停止现有服务器..." -ForegroundColor Yellow
.\stop_server.bat

# 构建项目
Write-Host "构建项目..." -ForegroundColor Yellow
$buildResult = dotnet build
if ($LASTEXITCODE -ne 0) {
    Write-Host "构建失败!" -ForegroundColor Red
    Write-Host $buildResult
    exit 1
}

Write-Host "构建成功!" -ForegroundColor Green

# 启动服务器（后台运行）
Write-Host "启动服务器..." -ForegroundColor Yellow
Start-Process -FilePath "dotnet" -ArgumentList "run" -WindowStyle Hidden

# 等待服务器启动
Write-Host "等待服务器启动..." -ForegroundColor Yellow
Start-Sleep -Seconds 10

# 检查服务器是否启动
$process = Get-Process -Name "AspNetCoreSseServer" -ErrorAction SilentlyContinue
if ($process) {
    Write-Host "服务器启动成功! PID: $($process.Id)" -ForegroundColor Green
} else {
    Write-Host "服务器启动失败!" -ForegroundColor Red
    exit 1
}

Write-Host "Application 层集成测试完成!" -ForegroundColor Green