using Microsoft.EntityFrameworkCore;
using TestServerWithHosting.DataAccess.Entities;

namespace TestServerWithHosting.DataAccess;

public class McpDbContext : DbContext
{
    public McpDbContext(DbContextOptions<McpDbContext> options) : base(options)
    {
    }

    public DbSet<User> Users { get; set; }
    public DbSet<Product> Products { get; set; }
    public DbSet<ProductStock> ProductStocks { get; set; }
    public DbSet<Order> Orders { get; set; }
    public DbSet<OrderDetail> OrderDetails { get; set; }
    public DbSet<ShopList> ShopLists { get; set; }
    public DbSet<ShopCart> ShopCarts { get; set; }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        base.OnModelCreating(modelBuilder);

        // Configure table names and entity mappings
        modelBuilder.Entity<User>(entity =>
        {
            entity.ToTable("AbpUsers");
            entity.HasKey(e => e.Id);
            entity.Property(e => e.UserName).HasMaxLength(256);
            entity.Property(e => e.Name).HasMaxLength(64);
            entity.Property(e => e.EmailAddress).HasMaxLength(256);
            entity.Property(e => e.PhoneNumber).HasMaxLength(32);
        });

        modelBuilder.Entity<Product>(entity =>
        {
            entity.ToTable("Product");
            entity.HasKey(e => e.Id);
            entity.Property(e => e.ProductName).HasMaxLength(500).IsRequired(false);
            entity.Property(e => e.ProductDescribe).HasMaxLength(4000).IsRequired(false);
        });

        modelBuilder.Entity<ProductStock>(entity =>
        {
            entity.ToTable("ProductStock");
            entity.HasKey(e => e.Id);
            entity.Property(e => e.StockName).HasMaxLength(500).IsRequired(false);
            entity.Property(e => e.Price).HasColumnType("decimal(18,2)");
            // 暂时忽略 StockNumber 字段，因为数据库表中可能不存在此列
            entity.Ignore(e => e.StockNumber);
        });

        modelBuilder.Entity<Order>(entity =>
        {
            entity.ToTable("Order");
            entity.HasKey(e => e.Id);
            entity.Property(e => e.OrderNumber).HasMaxLength(50).IsRequired(false);
            entity.Property(e => e.Total).HasColumnType("decimal(18,2)");
            entity.Property(e => e.RealityPay).HasColumnType("decimal(18,2)");
            entity.Property(e => e.ShipPrice).HasColumnType("decimal(18,2)");
        });

        modelBuilder.Entity<OrderDetail>(entity =>
        {
            entity.ToTable("OrderDetail");
            entity.HasKey(e => e.Id);
            entity.Property(e => e.ProductName).HasMaxLength(500).IsRequired(false);
            entity.Property(e => e.StockName).HasMaxLength(500).IsRequired(false);
            entity.Property(e => e.Price).HasColumnType("decimal(18,2)");
        });

        modelBuilder.Entity<ShopList>(entity =>
        {
            entity.ToTable("ShopList");
            entity.HasKey(e => e.Id);
            entity.Property(e => e.ShopName).HasMaxLength(500).IsRequired(false);
            entity.Property(e => e.Linkman).HasMaxLength(50).IsRequired(false);
            entity.Property(e => e.PhoneNumber).HasMaxLength(50).IsRequired(false);
            entity.Property(e => e.Remarks).HasMaxLength(4000).IsRequired(false);
        });

        modelBuilder.Entity<ShopCart>(entity =>
        {
            entity.ToTable("ShopCart");
            entity.HasKey(e => e.Id);
        });
    }
}