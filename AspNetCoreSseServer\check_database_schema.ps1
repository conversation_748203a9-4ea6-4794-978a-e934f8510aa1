# 数据库架构检查脚本
Write-Host "检查数据库架构..." -ForegroundColor Green

# 读取连接字符串
try {
    $config = Get-Content -Path "appsettings.json" | ConvertFrom-Json
    $connectionString = $config.ConnectionStrings.DefaultConnection
    
    if (-not $connectionString) {
        Write-Host "错误: 未找到数据库连接字符串" -ForegroundColor Red
        exit 1
    }
    
    Write-Host "连接字符串: $connectionString" -ForegroundColor Cyan
    
    # 提取服务器和数据库信息
    if ($connectionString -match "Server=([^;]+)") {
        $server = $matches[1]
        Write-Host "服务器: $server" -ForegroundColor Yellow
    }
    
    if ($connectionString -match "Database=([^;]+)") {
        $database = $matches[1]
        Write-Host "数据库: $database" -ForegroundColor Yellow
    }
    
    # 检查 ProductStock 表结构的 SQL 查询
    $sqlQuery = @"
SELECT 
    COLUMN_NAME,
    DATA_TYPE,
    IS_NULLABLE,
    COLUMN_DEFAULT
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_NAME = 'ProductStock'
ORDER BY ORDINAL_POSITION;
"@
    
    Write-Host ""
    Write-Host "ProductStock 表结构检查 SQL:" -ForegroundColor Yellow
    Write-Host $sqlQuery -ForegroundColor Gray
    
    Write-Host ""
    Write-Host "请手动执行以上 SQL 查询来检查 ProductStock 表是否包含 StockNumber 字段" -ForegroundColor Green
    Write-Host "如果没有 StockNumber 字段，需要执行以下 SQL 来添加:" -ForegroundColor Yellow
    
    $addColumnSql = @"
ALTER TABLE ProductStock 
ADD StockNumber INT NOT NULL DEFAULT 0;
"@
    
    Write-Host $addColumnSql -ForegroundColor Gray
    
} catch {
    Write-Host "错误: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""
Write-Host "架构检查完成!" -ForegroundColor Green