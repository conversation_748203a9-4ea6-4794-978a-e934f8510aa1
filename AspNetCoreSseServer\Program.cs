using Microsoft.EntityFrameworkCore;
using OpenTelemetry;
using OpenTelemetry.Metrics;
using OpenTelemetry.Trace;
using TestServerWithHosting.DataAccess;
using TestServerWithHosting.DataAccess.Entities;
using TestServerWithHosting.Resources;
using TestServerWithHosting.Tools;
using HeartFlower.Mall.Shop;
using HeartFlower.Mall.EntityFrameworkCore;
using Abp.EntityFrameworkCore;
using Abp.Modules;
using Abp.Dependency;

var builder = WebApplication.CreateBuilder(args);

// Configure database
var connectionString = builder.Configuration.GetConnectionString("DefaultConnection") 
    ?? "Server=**********; Database=HeartFlowerMall-Test; User Id=heartflower; Password=*************; TrustServerCertificate=True; Encrypt=False;";

builder.Services.AddDbContext<McpDbContext>(options =>
{
    options.UseSqlServer(connectionString);
});

// Register repository services
builder.Services.AddScoped<IRepository<Product, Guid>, Repository<Product, Guid>>();
builder.Services.AddScoped<IRepository<ProductStock, Guid>, Repository<ProductStock, Guid>>();
builder.Services.AddScoped<IRepository<Order, Guid>, Repository<Order, Guid>>();
builder.Services.AddScoped<IRepository<OrderDetail, Guid>, Repository<OrderDetail, Guid>>();
builder.Services.AddScoped<IRepository<ShopList, Guid>, Repository<ShopList, Guid>>();
builder.Services.AddScoped<IRepository<ShopCart, Guid>, Repository<ShopCart, Guid>>();
builder.Services.AddScoped<IRepository<User, long>, Repository<User, long>>();

// Register Application layer services
builder.Services.AddScoped<IProductAppService, ProductAppService>();
builder.Services.AddScoped<IShopListAppService, ShopListAppService>();
builder.Services.AddScoped<IOrderAppService, OrderAppService>();

// Configure MCP Server with all tools
builder.Services.AddMcpServer()
    .WithHttpTransport()
    .WithTools<EchoTool>()
    .WithTools<SampleLlmTool>()
    .WithTools<CalculatorTool>()
    .WithTools<ProductTools>()
    .WithTools<OrderTools>()
    .WithTools<ShopTools>()
    .WithTools<UserTools>()
    .WithResources<SimpleResourceType>();

builder.Services.AddOpenTelemetry()
    .WithTracing(b => b.AddSource("*")
        .AddAspNetCoreInstrumentation()
        .AddHttpClientInstrumentation())
    .WithMetrics(b => b.AddMeter("*")
        .AddAspNetCoreInstrumentation()
        .AddHttpClientInstrumentation());

var app = builder.Build();

app.MapMcp();

// Display startup information
var logger = app.Services.GetRequiredService<ILogger<Program>>();
logger.LogInformation("🚀 心花商城 MCP 服务器启动成功！");

Console.WriteLine("📡 MCP 端点: http://localhost:5000");
Console.WriteLine("🔧 可用工具: 商品管理、订单查询、用户管理、店铺管理、计算器、回声");
Console.WriteLine("✅ MCP Server 就绪，等待连接...");

app.Run();
