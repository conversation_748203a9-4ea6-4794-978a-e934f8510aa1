using System.ComponentModel;
using System.Text;
using Microsoft.EntityFrameworkCore;
using ModelContextProtocol.Server;
using TestServerWithHosting.DataAccess;
using TestServerWithHosting.DataAccess.Entities;
using HeartFlower.Mall.Shop;
using HeartFlower.Mall.Shop.Dto;
using Abp.Application.Services.Dto;

namespace TestServerWithHosting.Tools;

[McpServerToolType]
public class OrderTools
{
    private readonly IOrderAppService _orderAppService;
    private readonly IRepository<Order, Guid> _orderRepository;
    private readonly IRepository<OrderDetail, Guid> _orderDetailRepository;
    private readonly IRepository<ShopList, Guid> _shopListRepository;
    private readonly IRepository<User, long> _userRepository;

    public OrderTools(
        IOrderAppService orderAppService,
        IRepository<Order, Guid> orderRepository,
        IRepository<OrderDetail, Guid> orderDetailRepository,
        IRepository<ShopList, Guid> shopListRepository,
        IRepository<User, long> userRepository)
    {
        _orderAppService = orderAppService;
        _orderRepository = orderRepository;
        _orderDetailRepository = orderDetailRepository;
        _shopListRepository = shopListRepository;
        _userRepository = userRepository;
    }

    [McpServerTool, Description("搜索订单信息，支持按订单号搜索")]
    public async Task<string> SearchOrdersAsync(
        [Description("搜索关键词，通常是订单号，为空则返回所有订单")] string keyword = "",
        [Description("页码，从1开始")] int page = 1,
        [Description("每页数量")] int pageSize = 10)
    {
        try
        {
            // 使用 Application 层的接口进行订单搜索
            var input = new PlatFormOrderPageRequestDto
            {
                OrderNumber = keyword,
                MaxResultCount = pageSize,
                SkipCount = (page - 1) * pageSize
            };

            var result = await _orderAppService.GetPlatFormOrderList(input);
            
            if (result.Items == null || !result.Items.Any())
            {
                return $"未找到符合条件的订单。关键词: {keyword}";
            }

            var output = new StringBuilder();
            output.AppendLine($"订单搜索结果 (第{page}页, 共{result.TotalCount}条记录):");
            output.AppendLine($"关键词: {keyword}");
            output.AppendLine();

            foreach (var order in result.Items)
            {
                var statusText = GetOrderStatusText(order.OrderStatus);

                output.AppendLine($"订单号: {order.OrderNumber ?? "未设置"}");
                output.AppendLine($"用户: {order.UserName ?? "未知用户"}");
                output.AppendLine($"店铺: {order.ShopName ?? "未知店铺"}");
                output.AppendLine($"总金额: ¥{order.Total}");
                output.AppendLine($"实付金额: ¥{order.RealityPay}");
                output.AppendLine($"订单状态: {statusText}");
                output.AppendLine($"创建时间: {order.CreationTime:yyyy-MM-dd HH:mm:ss}");
                output.AppendLine("---");
            }

            return output.ToString();
        }
        catch (Exception ex)
        {
            return $"搜索订单时发生错误: {ex.Message}";
        }
    }

    [McpServerTool, Description("根据订单ID获取订单详细信息，包括订单商品明细")]
    public async Task<string> GetOrderByIdAsync(
        [Description("订单ID")] string orderId)
    {
        try
        {
            if (!Guid.TryParse(orderId, out var id))
            {
                return "错误：无效的订单ID格式";
            }

            var order = await _orderRepository.FirstOrDefaultAsync(o => o.Id == id && !o.IsDeleted);
            if (order == null)
            {
                return "未找到指定的订单";
            }

            var shop = await _shopListRepository.FirstOrDefaultAsync(s => s.Id == order.ShopId);
            var shopName = shop?.ShopName ?? "未知店铺";
            
            var user = await _userRepository.FirstOrDefaultAsync(u => u.Id == order.Userid);
            var userName = user?.UserName ?? "未知用户";

            // 获取订单详情
            var orderDetails = await _orderDetailRepository.GetAllListAsync(d => d.OrderId == order.Id && !d.IsDeleted);
            var detailInfo = new StringBuilder();
            foreach (var detail in orderDetails)
            {
                detailInfo.AppendLine($"  商品: {detail.ProductName ?? "未知商品"}");
                detailInfo.AppendLine($"  规格: {detail.StockName ?? "默认规格"}");
                detailInfo.AppendLine($"  数量: {detail.Amount}");
                detailInfo.AppendLine($"  单价: ¥{detail.Price}");
                detailInfo.AppendLine("  ---");
            }

            var statusText = GetOrderStatusText(order.OrderStatus);

            return $"订单详情：\n" +
                   $"订单ID: {order.Id}\n" +
                   $"订单号: {order.OrderNumber ?? "未设置"}\n" +
                   $"用户: {userName}\n" +
                   $"店铺: {shopName}\n" +
                   $"总金额: ¥{order.Total}\n" +
                   $"实付金额: ¥{order.RealityPay}\n" +
                   $"运费: ¥{order.ShipPrice}\n" +
                   $"订单状态: {statusText}\n" +
                   $"创建时间: {order.CreationTime:yyyy-MM-dd HH:mm:ss}\n" +
                   $"修改时间: {order.LastModificationTime?.ToString("yyyy-MM-dd HH:mm:ss") ?? "无"}\n" +
                   $"订单商品:\n{detailInfo}";
        }
        catch (Exception ex)
        {
            return $"获取订单详情时发生错误: {ex.Message}";
        }
    }

    private string GetOrderStatusText(int status)
    {
        return status switch
        {
            0 => "待付款",
            1 => "待发货", 
            2 => "待收货",
            3 => "已完成",
            4 => "已取消",
            5 => "退款中",
            6 => "已退款",
            _ => $"未知状态({status})"
        };
    }
}