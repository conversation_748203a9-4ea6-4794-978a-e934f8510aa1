<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <PublishAot>false</PublishAot>
  </PropertyGroup>

  <ItemGroup>
    <ProjectReference Include="..\doc\csharp-sdk-main\src\ModelContextProtocol.AspNetCore\ModelContextProtocol.AspNetCore.csproj" />
    <ProjectReference Include="..\src\HeartFlower.Mall.Application\HeartFlower.Mall.Application.csproj" />
    <ProjectReference Include="..\src\HeartFlower.Mall.Core\HeartFlower.Mall.Core.csproj" />
    <ProjectReference Include="..\src\HeartFlower.Mall.EntityFrameworkCore\HeartFlower.Mall.EntityFrameworkCore.csproj" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="OpenTelemetry.Exporter.OpenTelemetryProtocol" />
    <PackageReference Include="OpenTelemetry.Extensions.Hosting" />
    <PackageReference Include="OpenTelemetry.Instrumentation.AspNetCore" />
    <PackageReference Include="OpenTelemetry.Instrumentation.Http" />
    <PackageReference Include="Microsoft.EntityFrameworkCore" Version="8.0.0" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" Version="8.0.0" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="8.0.0" />
  </ItemGroup>

</Project>
